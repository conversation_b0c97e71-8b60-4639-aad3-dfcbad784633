﻿using Microsoft.AspNetCore.Mvc;
using PBC.HelpdeskService.Services;
using PBC.HelpdeskService.Models;

namespace PBC.HelpdeskService.Controllers
{

    /// <summary>
    /// Controller for Help Desk Request Page operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class HelpDeskServiceRequestController : Controller
    {
        private readonly IHelpDeskUserLandingPageServices _helpDeskUserLandingPageServices;
        private readonly IHelpDeskServiceRequestServices _helpDeskServiceRequestServices;
        private readonly IConfiguration _configuration;
        private readonly ILogger<HelpDeskServiceRequestController> _logger;

        public HelpDeskServiceRequestController(
            IHelpDeskUserLandingPageServices helpDeskUserLandingPageServices,
            IHelpDeskServiceRequestServices helpDeskServiceRequestServices,
            IConfiguration configuration,
            ILogger<HelpDeskServiceRequestController> logger)
        {
            _helpDeskUserLandingPageServices = helpDeskUserLandingPageServices;
            _helpDeskServiceRequestServices = helpDeskServiceRequestServices;
            _configuration = configuration;
            _logger = logger;
        }
        #region ::: InsertSR :::
        /// <summary>
        /// Insert Service Request
        /// </summary>
        /// <param name="request">Service request data</param>
        /// <returns>Insert result</returns>
        [HttpPost("insert-sr")]
        public async Task<IActionResult> InsertSR([FromBody] InsertSRRequest request, [FromQuery] string connectionString)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/insert-sr");

                var result = await _helpDeskServiceRequestServices.InsertSR(
                    request.ConnectionString ?? connectionString,
                    request.Reopen,
                    request.Data,
                    request.BranchID,
                    request.UserLanguageCode,
                    request.RequestParams,
                    request.HDAttachmentData,
                    request.Path,
                    request.ConnString ?? connectionString,
                    request.LogException,
                    request.Company_ID,
                    request.User_ID,
                    request.HelpDesk,
                    request.HelpLineNumber,
                    request.Language_ID,
                    request.Employee_ID,
                    request.MenuID,
                    request.HolidayDetails,
                    request.IsFromWebAPI
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in InsertSR");
                return StatusCode(500, "An error occurred while inserting service request");
            }
        }
        #endregion

        #region ::: InitialMode :::
        /// <summary>
        /// Get initial mode data
        /// </summary>
        /// <param name="request">Initial mode request</param>
        /// <returns>Initial mode data</returns>
        [HttpPost("initial-mode")]
        public IActionResult InitialMode([FromBody] InitialModeRequest request, [FromQuery] string connectionString)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/initial-mode");

                var result = _helpDeskServiceRequestServices.InitialMode(
                    request.UserEmployeeId,
                    request.CompanyId,
                    request.UserCulture,
                    request.ConnectionString ?? connectionString,
                    request.UnRegServiceRequestId,
                    request.PartyId,
                    request.ModelId,
                    request.SerialNumber,
                    request.RequestDesc,
                    request.ModelName,
                    request.Unique,
                    request.ServiceRequestId,
                    request.Reopen,
                    request.Mode,
                    request.StatusId,
                    request.CompanyIdAlt
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in InitialMode");
                return StatusCode(500, "An error occurred while getting initial mode data");
            }
        }
        #endregion

        #region ::: SelectSRDetails :::
        /// <summary>
        /// Select Service Request Details
        /// </summary>
        /// <param name="request">SR details request</param>
        /// <returns>SR details</returns>
        [HttpPost("select-sr-details")]
        public IActionResult SelectSRDetails([FromBody] SelectSRDetailsRequest request, [FromQuery] string connectionString)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/select-sr-details");

                var result = _helpDeskServiceRequestServices.SelectSRDetails(
                    request.User,
                    request.ServiceRequestID,
                    request.ChildTicketSequenceID,
                    request.BranchID,
                    request.ConnectionString ?? connectionString,
                    request.IsReopen,
                    request.IsOemDashboard,
                    request.OemCompanyId,
                    request.UserCulture,
                    request.GeneralLanguageCode,
                    request.UserLanguageCode,
                    request.MenuID,
                    request.LoggedInDateTime
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectSRDetails");
                return StatusCode(500, "An error occurred while selecting service request details");
            }
        }
        #endregion
        #region ::: GetProductDetails :::
        /// <summary>
        /// Get Product Details
        /// </summary>
        /// <param name="request">Product details request</param>
        /// <returns>Product details</returns>
        [HttpPost("get-product-details")]
        public IActionResult GetProductDetails([FromBody] GetProductDetailsFList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-product-details");

                var result = _helpDeskServiceRequestServices.GetProductDetails(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetProductDetails");
                return StatusCode(500, "An error occurred while getting product details");
            }
        }
        #endregion

        #region ::: GetActions :::
        /// <summary>
        /// Get Actions
        /// </summary>
        /// <param name="request">Actions request</param>
        /// <returns>Actions data</returns>
        [HttpPost("get-actions")]
        public async Task<IActionResult> GetActions([FromBody] GetActionsList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-actions");

                var result = await _helpDeskServiceRequestServices.GetActions(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetActions");
                return StatusCode(500, "An error occurred while getting actions");
            }
        }
        #endregion

        #region ::: EditSR :::
        /// <summary>
        /// Edit Service Request
        /// </summary>
        /// <param name="request">Edit SR request</param>
        /// <returns>Edit result</returns>
        [HttpPost("edit-sr")]
        public IActionResult EditSR([FromBody] EditSRRequest request, [FromQuery] string connectionString)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/edit-sr");

                var result = _helpDeskServiceRequestServices.EditSR(
                    request.Data,
                    request.BranchID,
                    request.UserLanguageCode,
                    request.RequestParams,
                    request.HDAttachmentData,
                    request.LoginCompanyID,
                    request.ConnString ?? connectionString,
                    request.LogException,
                    request.Company_ID,
                    request.User_ID,
                    request.HelpDesk,
                    request.HelpLineNumber,
                    request.Language_ID,
                    request.Employee_ID,
                    request.MenuID,
                    request.HolidayDetails,
                    request.IsFromWebAPI
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in EditSR");
                return StatusCode(500, "An error occurred while editing service request");
            }
        }
        #endregion
        #region ::: GetSRDetails :::
        /// <summary>
        /// Get Service Request Details
        /// </summary>
        /// <param name="request">SR details request</param>
        /// <returns>SR details</returns>
        [HttpPost("get-sr-details")]
        public async Task<IActionResult> GetSRDetails([FromBody] GetSRDetailsRequest request, [FromQuery] string connectionString)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-sr-details");

                var result = await _helpDeskServiceRequestServices.GetSRDetails(
                    request.User,
                    request.ServiceRequestID,
                    request.ChildTicket_Sequence_ID,
                    request.User_ID,
                    request.Company_ID,
                    request.Branch_ID,
                    request.ConnString ?? connectionString,
                    request.IsReopen,
                    request.IsOemDashboard,
                    request.OemCompanyId,
                    request.UserCulture,
                    request.GeneralLanguageCode,
                    request.UserLanguageCode,
                    request.MenuID,
                    request.LoggedInDateTime
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetSRDetails");
                return StatusCode(500, "An error occurred while getting service request details");
            }
        }
        #endregion

        #region ::: GetDetails :::
        /// <summary>
        /// Get Details for Date
        /// </summary>
        /// <param name="request">Get details request</param>
        /// <returns>Date details</returns>
        [HttpPost("get-details")]
        public IActionResult GetDetails([FromBody] GetDetailsRequest request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-details");

                var result = _helpDeskServiceRequestServices.GetDetails(
                    request.CallDate,
                    request.CompanyID,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetDetails");
                return StatusCode(500, "An error occurred while getting details");
            }
        }
        #endregion





    }
}
