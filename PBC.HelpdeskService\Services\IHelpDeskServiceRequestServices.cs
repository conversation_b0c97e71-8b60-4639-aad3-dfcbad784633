﻿using Microsoft.AspNetCore.Mvc;
using PBC.HelpdeskService.Models;

namespace PBC.HelpdeskService.Services
{
    public interface IHelpDeskServiceRequestServices
    {
        Task<IActionResult> InsertServiceRequestNegativeFeedbackEmailsAsync(InsertServiceRequestNegativeFeedbackEmailsList Obj, string constring, int LogException);

        // Service Request Functions
        Task<JsonResult> InsertSR(string constring, string Reopen, string Data, int BranchID, string UserLanguageCode, List<string> RequestParams,
            List<Attachements> HDAttachmentData, string Path, string connString, int LogException, int Company_ID, int User_ID
            , string HelpDesk, string HelpLineNumber, int Language_ID, int Employee_ID, int MenuID, string HolidayDetails
            , bool isFromWebAPI = false);

        JsonResult InitialMode(string userEmployeeId, int companyId, string userCulture, string connectionString,
            string? unRegServiceRequestId = null, string? partyId = null, string? modelId = null, string? serialNumber = null,
            string? requestDesc = null, string? modelName = null, string? unique = null, string? serviceRequestId = null,
            string? reopen = null, int? mode = null, int? statusId = null, int? companyIdAlt = null);

        object SelectSRDetails(GNM_User user, int serviceRequestID, int childTicketSequenceID, int branchID, string connectionString,
            bool isReopen, bool isOemDashboard, int? oemCompanyId, string userCulture, string generalLanguageCode,
            string userLanguageCode, int menuID, DateTime loggedInDateTime);

        JsonResult GetProductDetails(GetProductDetailsFList Obj, string constring, int LogException);

        Task<JsonResult> GetActions(GetActionsList Obj, string constring, int LogException);

        JsonResult EditSR(string Data, int BranchID, string UserLanguageCode, List<string> RequestParams,
             List<Attachements> HDAttachmentData, int LoginComapnyID, string connString, int LogException, int Company_ID, int User_ID
            , string HelpDesk, string HelpLineNumber, int Language_ID, int Employee_ID, int MenuID, string HolidayDetails
            , bool isFromWebAPI = false);

        Task<JsonResult> GetSRDetails(GNM_User user, int ServiceRequestID, int ChildTicket_Sequence_ID, int User_ID, int Company_ID,
            int Branch_ID, string connString, bool isReopen, bool isOemDashboard, int? oemCompanyId, string userCulture,
            string generalLanguageCode, string userLanguageCode, int menuID, DateTime loggedInDateTime);

        CallDateDetail GetDetails(DateTime? Calldate, int companyID, string connString, int LogException);
    }
}
